"""
Test script to connect to Fyers API and request a quote for a single option symbol.
Helps debug symbol formatting and API response issues.
"""
import sys
import os
import logging

sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
try:
    from fyers_client import FyersClient
except ImportError as e:
    raise ImportError("Could not import FyersClient from fyers_client.py. Make sure fyers_client.py is in the same directory as this script.") from e

from fyers_config import FyersConfig
from config_loader import get_config

# Set up logging to print to console
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fyers_test")

# Load configuration and initialize FyersClient
config = get_config()
fyers_config = FyersConfig(env_path=config.env_path)
fyers = FyersClient(env_path=config.env_path)

# Authenticate
if not fyers.authenticate():
    logger.error("Fyers login failed. Check credentials and authentication flow.")
    exit(1)

# --- Symbol Quote Request ---
# Test with a known-valid option symbol (update expiry/strike as needed)
test_symbol = "NSE:NIFTY25JUL22650CE"  # Example: NIFTY, expiry 2025-06-26, strike 25000, CE

logger.info(f"Requesting quote for symbol: {test_symbol}")

try:
    response = fyers.get_single_quote(test_symbol)
    print("API response:", response)
except Exception as e:
    logger.error(f"Error requesting quote: {e}")

# --- Weekly Selling Strategy Calculation Test ---
test_symbol = "NSE:BANKNIFTY25JUL56400CE"  # Example: NIFTY, expiry 2025-06-26, strike 24900, CE

logger.info(f"Testing weekly selling strategy calculation for symbol: {test_symbol}")

try:
    # This method may need to be updated if you want to fetch OHLC data for the underlying index
    # For now, just demonstrate a quote fetch for the option symbol
    response = fyers.get_single_quote(test_symbol)
    print(response)
    # Note: Fyers API may not provide OHLC data for option symbols. Use underlying index for valid data.
except Exception as e:
    logger.error(f"Error fetching weekly OHLC or calculating selling strategy: {e}")
