"""
Comprehensive tests for 'ALL' symbol functionality, pattern validation, and performance optimizations.
"""

import pytest
import tempfile
import os
import csv
from unittest.mock import patch, MagicMock
from symbol_parser import SymbolParser, OptionSymbol


class TestAllSymbolFunctionality:
    """Test cases for 'ALL' symbol handling."""
    
    def create_test_csv(self, symbols_data):
        """Create a temporary CSV file with test data."""
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        writer = csv.writer(temp_file)
        
        for i, symbol in enumerate(symbols_data):
            # Create a row with symbol in column J (index 9)
            row = [''] * 20  # Create enough columns
            row[9] = symbol  # Column J contains the NSE symbol
            writer.writerow(row)
        
        temp_file.close()
        return temp_file.name
    
    def test_all_symbol_detection(self):
        """Test that 'ALL' is properly detected and processed."""
        test_symbols = [
            'NSE:NIFTY25JUL19450CE',
            'NSE:BANKNIFTY25JUL32000PE',
            'NSE:RELIANCE25JUL2500CE',
            'NSE:INFY25JUL1500PE'
        ]
        
        csv_file = self.create_test_csv(test_symbols)
        
        try:
            # Test with 'ALL' in symbols list
            parser = SymbolParser(csv_file_path=csv_file, target_symbols=['ALL'])
            
            assert parser.use_all_symbols == True
            assert 'NIFTY' in parser.target_symbols
            assert 'BANKNIFTY' in parser.target_symbols
            assert 'RELIANCE' in parser.target_symbols
            assert 'INFY' in parser.target_symbols
            
        finally:
            os.unlink(csv_file)
    
    def test_all_symbol_ignores_other_symbols(self):
        """Test that when 'ALL' is present, other symbols are ignored."""
        test_symbols = [
            'NSE:NIFTY25JUL19450CE',
            'NSE:BANKNIFTY25JUL32000PE',
            'NSE:RELIANCE25JUL2500CE'
        ]
        
        csv_file = self.create_test_csv(test_symbols)
        
        try:
            # Test with 'ALL' and other symbols - should ignore other symbols
            parser = SymbolParser(csv_file_path=csv_file, target_symbols=['ALL', 'NIFTY', 'BANKNIFTY'])
            
            assert parser.use_all_symbols == True
            # Should contain all symbols from CSV, not just NIFTY and BANKNIFTY
            assert 'RELIANCE' in parser.target_symbols
            
        finally:
            os.unlink(csv_file)
    
    def test_without_all_symbol(self):
        """Test normal behavior without 'ALL' symbol."""
        test_symbols = [
            'NSE:NIFTY25JUL19450CE',
            'NSE:BANKNIFTY25JUL32000PE',
            'NSE:RELIANCE25JUL2500CE'
        ]
        
        csv_file = self.create_test_csv(test_symbols)
        
        try:
            # Test without 'ALL' - should use specified symbols only
            parser = SymbolParser(csv_file_path=csv_file, target_symbols=['NIFTY', 'BANKNIFTY'])
            
            assert parser.use_all_symbols == False
            assert parser.target_symbols == {'NIFTY', 'BANKNIFTY'}
            assert 'RELIANCE' not in parser.target_symbols
            
        finally:
            os.unlink(csv_file)


class TestPatternValidation:
    """Test cases for strict pattern validation."""
    
    def test_valid_patterns(self):
        """Test that valid patterns are accepted."""
        parser = SymbolParser(target_symbols=['NIFTY', 'BANKNIFTY', 'RELIANCE'])
        
        valid_symbols = [
            'NSE:NIFTY25JUL19450CE',
            'NSE:BANKNIFTY25SEP32000PE',
            'NSE:RELIANCE25AUG2500CE',
            'NIFTY25DEC50000PE',  # Without NSE: prefix
        ]
        
        for symbol in valid_symbols:
            result = parser.parse_symbol_from_nse_format(symbol)
            assert result is not None, f"Valid symbol {symbol} should be parsed successfully"
            assert result.underlying in ['NIFTY', 'BANKNIFTY', 'RELIANCE']
    
    def test_invalid_patterns(self):
        """Test that invalid patterns are rejected."""
        parser = SymbolParser(target_symbols=['NIFTY', 'BANKNIFTY'])
        
        invalid_symbols = [
            'NSE:NIFTY25J19450CE',      # Invalid month format (J instead of JUL)
            'NSE:NIFTY5JUL19450CE',     # Invalid year format (5 instead of 25)
            'NSE:NIFTY25JUL19450XX',    # Invalid option type (XX instead of CE/PE)
            'NSE:NIFTY25JUL-19450CE',   # Negative strike price
            'NSE:NIFTY25JULY19450CE',   # Invalid month format (JULY instead of JUL)
            'NSE:NIFTY2025JUL19450CE',  # Invalid year format (2025 instead of 25)
            'NSE:UNKNOWN25JUL19450CE',  # Unknown underlying
            'NSE:NIFTY25XYZ19450CE',    # Invalid month (XYZ)
            'NSE:NIFTY25JUL0CE',        # Zero strike price
        ]
        
        for symbol in invalid_symbols:
            result = parser.parse_symbol_from_nse_format(symbol)
            assert result is None, f"Invalid symbol {symbol} should be rejected"
    
    def test_month_validation(self):
        """Test that only valid months are accepted."""
        parser = SymbolParser(target_symbols=['NIFTY'])
        
        valid_months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                       'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']
        
        for month in valid_months:
            symbol = f'NSE:NIFTY25{month}19450CE'
            result = parser.parse_symbol_from_nse_format(symbol)
            assert result is not None, f"Valid month {month} should be accepted"
        
        invalid_months = ['XXX', 'ABC', '123', 'JU', 'JULY']
        for month in invalid_months:
            symbol = f'NSE:NIFTY25{month}19450CE'
            result = parser.parse_symbol_from_nse_format(symbol)
            assert result is None, f"Invalid month {month} should be rejected"


class TestPerformanceOptimizations:
    """Test cases for performance optimizations."""

    def create_test_csv(self, symbols_data):
        """Create a temporary CSV file with test data."""
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        writer = csv.writer(temp_file)

        for i, symbol in enumerate(symbols_data):
            # Create a row with symbol in column J (index 9)
            row = [''] * 20  # Create enough columns
            row[9] = symbol  # Column J contains the NSE symbol
            writer.writerow(row)

        temp_file.close()
        return temp_file.name

    def create_large_test_csv(self, num_symbols=1000):
        """Create a large CSV file for performance testing."""
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        writer = csv.writer(temp_file)
        
        underlyings = ['NIFTY', 'BANKNIFTY', 'RELIANCE', 'INFY', 'TCS']
        months = ['JUL', 'AUG', 'SEP']
        option_types = ['CE', 'PE']
        
        for i in range(num_symbols):
            underlying = underlyings[i % len(underlyings)]
            month = months[i % len(months)]
            option_type = option_types[i % len(option_types)]
            strike = 1000 + (i * 50)
            
            symbol = f'NSE:{underlying}25{month}{strike}{option_type}'
            
            # Create a row with symbol in column J (index 9)
            row = [''] * 20
            row[9] = symbol
            writer.writerow(row)
        
        temp_file.close()
        return temp_file.name
    
    def test_symbol_limit_functionality(self):
        """Test that symbol limiting works correctly."""
        csv_file = self.create_large_test_csv(1000)
        
        try:
            parser = SymbolParser(csv_file_path=csv_file, target_symbols=['NIFTY', 'BANKNIFTY'])
            
            # Test with limit
            symbols = parser.load_symbols_from_csv(limit_symbols=50)
            assert len(symbols) <= 50, "Symbol limit should be respected"
            
            # Test without limit
            all_symbols = parser.load_symbols_from_csv()
            assert len(all_symbols) > 50, "Should load more symbols without limit"
            
        finally:
            os.unlink(csv_file)
    
    def test_optimized_pattern_for_many_symbols(self):
        """Test that optimized pattern is used for many symbols."""
        # Create CSV with many different underlyings (25 to trigger optimization)
        underlyings = [f'STOCK{i}' for i in range(25)]  # 25 different stocks
        test_symbols = []

        # Create multiple symbols for each underlying to ensure they're detected
        for underlying in underlyings:
            test_symbols.extend([
                f'NSE:{underlying}25JUL1000CE',
                f'NSE:{underlying}25JUL1000PE',
                f'NSE:{underlying}25AUG1500CE'
            ])

        csv_file = self.create_test_csv(test_symbols)

        try:
            # This should trigger the optimized pattern (>20 symbols)
            parser = SymbolParser(csv_file_path=csv_file, target_symbols=['ALL'])

            # Should use general pattern for performance
            assert parser.use_all_symbols == True
            assert len(parser.target_symbols) > 20, f"Expected >20 symbols, got {len(parser.target_symbols)}: {parser.target_symbols}"

            # Verify the pattern is optimized (general pattern)
            expected_pattern = r'^([A-Z0-9&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            assert parser.symbol_pattern == expected_pattern, f"Expected optimized pattern, got: {parser.symbol_pattern}"

        finally:
            os.unlink(csv_file)
    
    def test_performance_logging(self):
        """Test that performance logging works correctly."""
        csv_file = self.create_large_test_csv(100)
        
        try:
            parser = SymbolParser(csv_file_path=csv_file, target_symbols=['NIFTY'])
            
            with patch('symbol_parser.logger') as mock_logger:
                symbols = parser.load_symbols_from_csv()
                
                # Should have logged performance stats
                mock_logger.info.assert_called()
                
                # Check that performance stats were logged
                calls = [call.args[0] for call in mock_logger.info.call_args_list]
                performance_calls = [call for call in calls if 'Performance stats' in call]
                assert len(performance_calls) > 0, "Performance stats should be logged"
                
        finally:
            os.unlink(csv_file)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
