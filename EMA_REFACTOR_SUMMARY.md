# EMA Indicator Refactoring Summary

## Overview
The EMA (Exponential Moving Average) indicator logic has been completely refactored to implement proper filtering based on historical OHLC data. The EMA filter is now applied as the final filter after Volume and LTP filters, and only symbols where the current price is passing through the short-period EMA are selected.

## Key Changes Made

### 1. Configuration Updates (`config.yaml`)
- **Added Timeframe Settings**:
  ```yaml
  # Timeframe Settings
  timeframe:
    interval: 1  # As shown in interval map
    days_to_fetch: 15  # Days
  ```

### 2. Configuration Loader Updates (`config_loader.py`)
- **Added new properties**:
  - `timeframe_interval`: Get timeframe interval for OHLC data
  - `days_to_fetch`: Get number of days to fetch for historical data

### 3. Fyers Client Enhancements (`fyers_client.py`)
- **Added INTERVAL_MAP**: Mapping for different timeframe intervals
- **Added OHLCData dataclass**: Structure for historical OHLC data
- **Added get_historical_data() method**: Fetches historical OHLC data from Fyers API
  - Supports multiple timeframes (1, 3, 5, 15, 30, 60 minutes, 1D)
  - Configurable date range based on `days_to_fetch`
  - Returns structured OHLCData objects

### 4. Technical Indicators Enhancements (`technical_indicators.py`)
- **Added is_price_passing_through_ema() method**: 
  - Checks if current price is within tolerance range of EMA
  - Configurable tolerance percentage (default 0.5%)
  - Returns True if price is "passing through" the EMA line
- **Added is_symbol_passing_through_ema() method** to EMAAnalyzer class:
  - Wrapper method for easy integration with scanner

### 5. Index Scanner Refactoring (`index_scanner.py`)
- **Complete refactoring of apply_filters() method**:
  - **Sequential Filtering**: Volume → LTP → EMA (if enabled)
  - **Historical Data Integration**: Fetches real OHLC data for each symbol
  - **EMA Filter Logic**: Only symbols passing through short-period EMA are selected
  - **Enhanced Logging**: Detailed statistics for each filter stage
  - **Error Handling**: Graceful handling of insufficient historical data

## Filter Flow

```
Market Data Input
       ↓
1. Volume Filter (min_volume ≤ volume ≤ max_volume)
       ↓
2. LTP Filter (min_ltp ≤ ltp ≤ max_ltp)
       ↓
3. EMA Filter (if enabled)
   - Fetch historical OHLC data (15 days, 1-minute interval)
   - Calculate EMA(9) using close prices
   - Check if current LTP is within 0.5% of EMA(9)
       ↓
Final Filtered Symbols
```

## EMA Filter Logic Details

### Data Requirements
- **Historical Data**: 15 days of OHLC data
- **Minimum Data Points**: At least 9 data points for EMA(9) calculation
- **Timeframe**: 1-minute intervals (configurable)

### Filtering Criteria
- **EMA Period**: 9 (short period from configuration)
- **Tolerance**: 0.5% of EMA value
- **Logic**: Current LTP must be within ±0.5% of EMA(9) value
- **Formula**: `EMA(9) - 0.5% ≤ Current LTP ≤ EMA(9) + 0.5%`

### Example
```
EMA(9) = 2575.60
Tolerance = 0.5% = 12.88
Range = [2562.72, 2588.48]
Current LTP = 2580.00 → PASS (within range)
Current LTP = 2600.00 → FAIL (outside range)
```

## Configuration Options

### Timeframe Settings
```yaml
timeframe:
  interval: 1     # Minutes (1, 3, 5, 15, 30, 60) or "1D" for daily
  days_to_fetch: 15  # Number of days of historical data
```

### EMA Settings
```yaml
ema_indicator:
  short_period: 9    # EMA period for filtering
  long_period: 21    # EMA period for analysis (not used in filtering)
  enabled: true      # Enable/disable EMA filtering
```

## Benefits of the Refactored Implementation

1. **Accurate EMA Calculation**: Uses proper historical OHLC data instead of limited current data
2. **Configurable Timeframes**: Supports multiple intervals and historical periods
3. **Proper Filter Sequence**: EMA applied as final filter after volume/LTP screening
4. **Performance Optimized**: Only fetches historical data for symbols that pass initial filters
5. **Robust Error Handling**: Gracefully handles API failures and insufficient data
6. **Detailed Logging**: Comprehensive statistics for each filter stage
7. **Flexible Tolerance**: Configurable tolerance for EMA proximity detection

## Usage

The EMA filter is automatically applied when `ema_indicator.enabled` is set to `true` in the configuration. The scanner will:

1. Apply volume and LTP filters first
2. For remaining symbols, fetch historical OHLC data
3. Calculate EMA(9) using close prices
4. Filter symbols where current price is passing through EMA(9)
5. Return only symbols that pass all filters

## Testing

A comprehensive test script (`test_ema_integration.py`) has been created to verify:
- Configuration loading
- EMA calculation accuracy
- Price passing through EMA detection
- Integration with the filtering system

The refactored implementation ensures that only options symbols with prices currently interacting with their short-period EMA are selected, providing more meaningful technical analysis-based filtering.