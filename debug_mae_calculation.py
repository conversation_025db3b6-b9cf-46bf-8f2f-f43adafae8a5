#!/usr/bin/env python3
"""
Debug script to analyze MAE calculation discrepancies.
"""

import logging
import pandas as pd
import ta
from config_loader import get_config
from fyers_client import FyersClient
from technical_indicators import MAEAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_different_ema_approaches():
    """Test different EMA calculation approaches to match expected values."""
    
    # Load configuration
    config = get_config()
    logger.info("Configuration loaded successfully")
    
    # Initialize Fyers client
    fyers = FyersClient()
    assert fyers.authenticate(), "Fyers authentication failed!"
    
    # Test symbols with expected values
    test_cases = {
        "NSE:NIFTY25JUL22400CE": 3153.90,
        "NSE:NIFTY25JUL22650CE": 2880.82
    }
    
    for symbol, expected_mae in test_cases.items():
        logger.info(f"\n{'='*60}")
        logger.info(f"Analyzing symbol: {symbol}")
        logger.info(f"Expected MAE: {expected_mae}")
        logger.info(f"{'='*60}")
        
        # Test different timeframes and data periods
        test_configs = [
            (60, 30, "60min"),   # Current config
            (1440, 30, "Daily"), # Daily data
            (60, 60, "60min_60days"),
            (1440, 60, "Daily_60days")
        ]

        for interval, days, desc in test_configs:
            logger.info(f"\n--- Testing with {desc} ({days} days) ---")
            ohlc_data = fyers.get_historical_data(symbol, interval=interval, days_to_fetch=days)
            
            if not ohlc_data or len(ohlc_data) < 18:  # Need at least 18 points for double EMA(9)
                logger.warning(f"Not enough data for {days} days: {len(ohlc_data) if ohlc_data else 0} points")
                continue
                
            logger.info(f"Data points: {len(ohlc_data)}")
            
            # Get close prices
            close_prices = pd.Series([d.close for d in ohlc_data])
            logger.info(f"Close price range: {close_prices.min():.2f} - {close_prices.max():.2f}")
            logger.info(f"Last 3 closes: {close_prices.iloc[-3:].tolist()}")
            
            # Test different EMA calculation methods
            test_ema_variations(close_prices, expected_mae, symbol, f"{desc}_{days}days")

def test_ema_variations(close_prices, expected_mae, symbol, days):
    """Test different EMA calculation variations."""
    
    results = []
    
    # Method 1: Current implementation (adjust=False)
    ema1 = ta.trend.ema_indicator(close_prices, window=9, fillna=False)
    mae1 = ta.trend.ema_indicator(ema1, window=9, fillna=False)
    final_mae1 = mae1.iloc[-1] if not mae1.empty and not pd.isna(mae1.iloc[-1]) else None
    results.append(("Current (adjust=False)", final_mae1))
    
    # Method 2: Using pandas ewm with adjust=True
    ema2 = close_prices.ewm(span=9, adjust=True).mean()
    mae2 = ema2.ewm(span=9, adjust=True).mean()
    final_mae2 = mae2.iloc[-1] if not mae2.empty and not pd.isna(mae2.iloc[-1]) else None
    results.append(("Pandas ewm (adjust=True)", final_mae2))
    
    # Method 3: Using pandas ewm with adjust=False
    ema3 = close_prices.ewm(span=9, adjust=False).mean()
    mae3 = ema3.ewm(span=9, adjust=False).mean()
    final_mae3 = mae3.iloc[-1] if not mae3.empty and not pd.isna(mae3.iloc[-1]) else None
    results.append(("Pandas ewm (adjust=False)", final_mae3))
    
    # Method 4: Using alpha directly
    alpha = 2 / (9 + 1)
    ema4 = close_prices.ewm(alpha=alpha, adjust=False).mean()
    mae4 = ema4.ewm(alpha=alpha, adjust=False).mean()
    final_mae4 = mae4.iloc[-1] if not mae4.empty and not pd.isna(mae4.iloc[-1]) else None
    results.append(("Alpha method (adjust=False)", final_mae4))
    
    # Method 5: Using alpha with adjust=True
    ema5 = close_prices.ewm(alpha=alpha, adjust=True).mean()
    mae5 = ema5.ewm(alpha=alpha, adjust=True).mean()
    final_mae5 = mae5.iloc[-1] if not mae5.empty and not pd.isna(mae5.iloc[-1]) else None
    results.append(("Alpha method (adjust=True)", final_mae5))
    
    # Method 6: Manual EMA calculation
    def manual_ema(series, period):
        alpha = 2 / (period + 1)
        result = [series.iloc[0]]  # Start with first value
        for i in range(1, len(series)):
            result.append(alpha * series.iloc[i] + (1 - alpha) * result[-1])
        return pd.Series(result, index=series.index)
    
    ema6 = manual_ema(close_prices, 9)
    mae6 = manual_ema(ema6, 9)
    final_mae6 = mae6.iloc[-1] if not mae6.empty and not pd.isna(mae6.iloc[-1]) else None
    results.append(("Manual EMA", final_mae6))
    
    # Print results
    logger.info(f"\nMAE calculation results for {symbol} ({days} days):")
    logger.info(f"Expected: {expected_mae}")
    
    best_match = None
    best_diff = float('inf')
    
    for method, value in results:
        if value is not None:
            diff = abs(value - expected_mae)
            logger.info(f"{method:25}: {value:10.2f} (diff: {diff:6.2f})")
            if diff < best_diff:
                best_diff = diff
                best_match = (method, value)
        else:
            logger.info(f"{method:25}: {'None':>10} (insufficient data)")
    
    if best_match:
        logger.info(f"\nBest match: {best_match[0]} with difference of {best_diff:.2f}")
        if best_diff < 2.0:
            logger.info("✓ Found a method that matches expected value!")
        else:
            logger.warning("✗ No method matches expected value within tolerance")

if __name__ == "__main__":
    test_different_ema_approaches()
