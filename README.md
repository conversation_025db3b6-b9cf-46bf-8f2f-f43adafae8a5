# Index Scanner

A Python application that scans NIFTY and BANKNIFTY options symbols, filters them based on configurable criteria, and generates detailed reports.

## Features

- **Symbol Scanning**: Automatically extracts NIFTY and BANKNIFTY options for current month, next month, and next-next month from NSE_FO.csv
- **Market Data**: Fetches real-time market data using Fyers API v3
- **Configurable Filters**: Filter symbols based on volume and LTP (Last Traded Price) criteria
- **Report Generation**: Creates CSV reports and summary statistics
- **Daily Authentication**: Handles Fyers API authentication with daily token caching

## Requirements

- Python 3.7+
- Required packages: `pyyaml`, `fyers-apiv3`
- NSE_FO.csv file with F&O instrument data
- Fyers API credentials in .env file

## Installation

1. Install required packages:
```bash
pip install pyyaml fyers-apiv3
```

2. Ensure you have the following files in the project directory:
   - `config.yaml` - Configuration file
   - `NSE_FO.csv` - NSE F&O instruments data
   - `.env` - Fyers API credentials (path specified in config.yaml)

## Configuration

Edit `config.yaml` to customize the scanning parameters:

```yaml
general:
  env_path: "C:/Users/<USER>/Desktop/Python/.env"
  output_dir: "reports"

symbols:
  - "NIFTY"
  - "BANKNIFTY"

options_filter:
  min_volume: 5
  min_ltp_price: 2500
  max_ltp_price: 5000
```

### Configuration Parameters

- **env_path**: Path to your .env file containing Fyers API credentials
- **output_dir**: Directory where reports will be saved
- **symbols**: List of underlying symbols to scan (NIFTY, BANKNIFTY)
- **min_volume**: Minimum trading volume filter
- **min_ltp_price**: Minimum LTP price filter
- **max_ltp_price**: Maximum LTP price filter

## Usage

### Run the Scanner

```bash
python main.py
```

### Test the Implementation

```bash
python test_scanner.py
```

## Output

The scanner generates two types of reports in the configured output directory:

### 1. CSV Report (`index_scan_YYYYMMDD_HHMMSS.csv`)

Contains filtered symbols with the following columns:
- `strike` - Strike price
- `expiry_date` - Expiry date
- `type` - Option type (CE/PE)
- `symbol` - Full NSE symbol
- `LTP` - Last traded price
- `volume` - Trading volume
- `open` - Opening price
- `high` - Day's high price
- `low` - Day's low price
- `close` - Closing price
- `prev_close` - Previous day's close
- `change` - Price change
- `change_percent` - Percentage change

### 2. Summary Report (`index_scan_summary_YYYYMMDD_HHMMSS.txt`)

Contains:
- Summary statistics (total symbols, breakdown by underlying and option type)
- Top 10 symbols by volume
- Top 10 symbols by LTP

## Project Structure

```
index_scanner/
├── main.py                 # Main entry point
├── config_loader.py        # Configuration management
├── symbol_parser.py        # CSV parsing and symbol extraction
├── fyers_client.py         # Fyers API client
├── fyers_config.py         # Fyers authentication (from Reference)
├── index_scanner.py        # Core scanning logic
├── report_generator.py     # Report generation
├── test_scanner.py         # Test suite
├── config.yaml             # Configuration file
├── NSE_FO.csv             # NSE F&O instruments data
└── reports/               # Generated reports directory
```

## How It Works

1. **Configuration Loading**: Loads settings from `config.yaml`
2. **Symbol Parsing**: Extracts relevant NIFTY/BANKNIFTY symbols from NSE_FO.csv for target months
3. **Authentication**: Authenticates with Fyers API (daily token caching)
4. **Market Data**: Fetches real-time quotes for filtered symbols
5. **Filtering**: Applies volume and LTP filters based on configuration
6. **Reporting**: Generates CSV and summary reports

## Filtering Logic

Symbols are included in the final report if they meet ALL of the following criteria:
- Underlying symbol is in the configured symbols list (NIFTY, BANKNIFTY)
- Expiry date is in current month, next month, or next-next month
- Trading volume >= configured minimum volume
- LTP is within the configured price range

## Error Handling

- Comprehensive logging with file rotation
- Graceful handling of API failures
- Configuration validation
- Missing file detection

## Notes

- The scanner processes options for 3 months: current month + next 2 months
- Fyers API authentication is required only once per day
- Large symbol lists are processed in batches to respect API limits
- All prices are rounded to 2 decimal places in reports

## Troubleshooting

1. **Authentication Issues**: Ensure your .env file contains valid Fyers API credentials
2. **No Symbols Found**: Check if NSE_FO.csv is present and contains data for target months
3. **Filter Too Restrictive**: Adjust volume and LTP filters in config.yaml
4. **API Limits**: The scanner processes symbols in batches to avoid rate limits

For detailed logs, check the log files generated in the application directory.
