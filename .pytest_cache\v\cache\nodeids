["test_all_symbol_functionality.py::TestAllSymbolFunctionality::test_all_symbol_detection", "test_all_symbol_functionality.py::TestAllSymbolFunctionality::test_all_symbol_ignores_other_symbols", "test_all_symbol_functionality.py::TestAllSymbolFunctionality::test_without_all_symbol", "test_all_symbol_functionality.py::TestPatternValidation::test_invalid_patterns", "test_all_symbol_functionality.py::TestPatternValidation::test_month_validation", "test_all_symbol_functionality.py::TestPatternValidation::test_valid_patterns", "test_all_symbol_functionality.py::TestPerformanceOptimizations::test_optimized_pattern_for_many_symbols", "test_all_symbol_functionality.py::TestPerformanceOptimizations::test_performance_logging", "test_all_symbol_functionality.py::TestPerformanceOptimizations::test_symbol_limit_functionality", "test_ce_pe_pairing.py::test_base_symbol_extraction", "test_ce_pe_pairing.py::test_ce_pe_pairing_disabled", "test_ce_pe_pairing.py::test_ce_pe_pairing_enabled", "test_ce_pe_pairing.py::test_one_to_one_ce_pe_pairing", "test_ce_pe_pairing.py::test_price_percentage_filtering", "test_ce_pe_pairing.py::test_same_expiry_month_requirement"]