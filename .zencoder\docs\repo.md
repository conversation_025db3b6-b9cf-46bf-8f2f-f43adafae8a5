# Index Scanner Information

## Summary
A Python application that scans NIFTY and BANKNIFTY options symbols, filters them based on configurable criteria, and generates detailed reports. The tool fetches real-time market data using the Fyers API, applies volume and price filters, and produces CSV reports with filtered symbols and summary statistics.

## Structure
- **Root Directory**: Main Python modules and configuration files
- **reports/**: Output directory for generated CSV reports and summary files
- **Reference/**: Contains reference implementations and utility modules

## Language & Runtime
**Language**: Python
**Version**: 3.7+
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- `pyyaml`: YAML configuration file parsing
- `fyers-apiv3`: Fyers trading API client
- `python-dotenv`: Environment variable management

**Development Dependencies**:
- Standard library modules: `logging`, `csv`, `re`, `datetime`, `typing`

## Build & Installation
```bash
pip install pyyaml fyers-apiv3 python-dotenv
```

## Configuration
**Main Config File**: `config.yaml`
**Structure**:
- `general`: Environment path and output directory settings
- `symbols`: List of underlying symbols to scan (NIFTY, BANKNIFTY)
- `options_filter`: Volume and price filter settings
- `ema_indicator`: Technical indicator configuration

**Required Files**:
- `NSE_FO.csv`: NSE F&O instruments data
- `.env`: Fyers API credentials (path specified in config.yaml)

## Main Components
- **main.py**: Entry point that orchestrates the scanning process
- **index_scanner.py**: Core scanning logic for filtering options
- **symbol_parser.py**: Parses and extracts symbols from NSE_FO.csv
- **fyers_client.py**: Client for interacting with Fyers API
- **fyers_config.py**: Handles Fyers authentication and token management
- **report_generator.py**: Generates CSV reports and summary statistics
- **technical_indicators.py**: Implements technical analysis indicators

## Testing
**Framework**: Custom test implementation
**Test Files**:
- `test_scanner.py`: Tests the complete scanner flow with mock data
- `test_fyers_symbol.py`: Tests symbol parsing functionality

**Run Command**:
```bash
python test_scanner.py
```

## Data Flow
1. **Configuration Loading**: Loads settings from `config.yaml`
2. **Symbol Parsing**: Extracts relevant NIFTY/BANKNIFTY symbols from NSE_FO.csv
3. **Authentication**: Authenticates with Fyers API (with daily token caching)
4. **Market Data**: Fetches real-time quotes for filtered symbols
5. **Filtering**: Applies volume and LTP filters based on configuration
6. **Reporting**: Generates CSV and summary reports in the `reports/` directory

## Error Handling
- Comprehensive logging with file rotation
- Graceful handling of API failures
- Configuration validation
- Missing file detection