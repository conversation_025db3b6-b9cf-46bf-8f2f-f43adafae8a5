
"""
Test script for the Index Scanner without requiring <PERSON>yers authentication.
Creates mock data to test the complete flow.
"""

import logging
import os
from datetime import datetime
from typing import Dict, List

from config_loader import get_config
from symbol_parser import SymbolParser
from index_scanner import FilteredSymbol
from report_generator import ReportGenerator
from fyers_client import MarketData

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Get the absolute path to the directory where this script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CSV_FILE_PATH = os.path.join(SCRIPT_DIR, "NSE_FO.csv")

def create_mock_market_data(symbols: List[str]) -> Dict[str, MarketData]:
    """Create mock market data for testing."""
    import random
    
    market_data = {}
    
    for symbol in symbols[:20]:  # Test with first 20 symbols
        # Generate realistic mock data
        ltp = random.uniform(2000, 6000)  # LTP in range
        volume = random.randint(1, 100)  # Some will pass volume filter, some won't
        open_price = ltp * random.uniform(0.95, 1.05)
        high = max(ltp, open_price) * random.uniform(1.0, 1.02)
        low = min(ltp, open_price) * random.uniform(0.98, 1.0)
        close = ltp
        prev_close = ltp * random.uniform(0.98, 1.02)
        change = ltp - prev_close
        change_percent = (change / prev_close * 100) if prev_close > 0 else 0
        
        market_data[symbol] = MarketData(
            symbol=symbol,
            ltp=ltp,
            volume=volume,
            open_price=open_price,
            high=high,
            low=low,
            close=close,
            prev_close=prev_close,
            change=change,
            change_percent=change_percent
        )
    
    return market_data

def test_complete_flow():
    """Test the complete scanner flow with mock data."""
    try:
        print("Testing Index Scanner Complete Flow")
        print("=" * 50)
        
        # Step 1: Load configuration
        print("1. Loading configuration...")
        config = get_config()
        
        if not config.validate_config():
            print("Configuration validation failed")
            return False

        print(f"Configuration loaded - Symbols: {config.symbols}")
        
        # Step 2: Parse symbols
        print("2. Parsing symbols from CSV...")
        parser = SymbolParser(csv_file_path=CSV_FILE_PATH)
        symbols = parser.get_symbols_for_scanning(config.symbols)
        
        print(f"Found {len(symbols)} symbols for scanning")

        # Step 3: Create mock market data
        print("3. Creating mock market data...")
        mock_data = create_mock_market_data(symbols)

        print(f"Created mock data for {len(mock_data)} symbols")
        
        # Step 4: Apply filters (simulate scanner logic)
        print("4. Applying filters...")
        filtered_symbols = []
        
        for symbol, data in mock_data.items():
            # Apply volume filter
            if data.volume < config.min_volume:
                continue
            
            # Apply LTP filter
            if data.ltp < config.min_ltp_price or data.ltp > config.max_ltp_price:
                continue
            
            # Extract strike price and other info
            import re
            clean_symbol = symbol.replace('NSE:', '')
            pattern = r'^([A-Z&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            match = re.match(pattern, clean_symbol)
            
            if match:
                year = match.group(2)
                month = match.group(3)
                strike = float(match.group(4))
                option_type = match.group(5)
                expiry_date = f"20{year}-{month}"
                
                filtered_symbol = FilteredSymbol(
                    strike=strike,
                    expiry_date=expiry_date,
                    option_type=option_type,
                    symbol=symbol,
                    ltp=data.ltp,
                    volume=data.volume,
                    open_price=data.open_price,
                    high=data.high,
                    low=data.low,
                    close=data.close,
                    prev_close=data.prev_close,
                    change=data.change,
                    change_percent=data.change_percent,
                    ema_short=None,
                    ema_long=None,
                    ema_signal="NEUTRAL"
                )
                
                filtered_symbols.append(filtered_symbol)
        
        print(f"Filtered to {len(filtered_symbols)} symbols matching criteria")

        # Step 5: Generate summary stats
        print("5. Generating summary statistics...")
        summary_stats = {
            'total_symbols': len(filtered_symbols),
            'nifty_symbols': sum(1 for s in filtered_symbols if 'NIFTY' in s.symbol and 'BANKNIFTY' not in s.symbol),
            'banknifty_symbols': sum(1 for s in filtered_symbols if 'BANKNIFTY' in s.symbol),
            'ce_options': sum(1 for s in filtered_symbols if s.option_type == 'CE'),
            'pe_options': sum(1 for s in filtered_symbols if s.option_type == 'PE')
        }

        print(f"Summary stats: {summary_stats}")
        
        # Step 6: Generate reports
        print("6. Generating reports...")
        report_generator = ReportGenerator(output_dir=config.output_dir)
        
        if filtered_symbols:
            report_files = report_generator.generate_full_report(filtered_symbols, summary_stats)
            print(f"Reports generated:")
            print(f"   - CSV: {report_files['csv_report']}")
            print(f"   - Summary: {report_files['summary_report']}")
        else:
            print("No symbols to report (all filtered out)")

        print("\nTest completed successfully!")
        return True

    except Exception as e:
        print(f"Test failed: {e}")
        logger.error(f"Test failed: {e}", exc_info=True)
        return False

def test_individual_components():
    """Test individual components separately."""
    print("\nTesting Individual Components")
    print("=" * 40)

    # Test 1: Config loader
    try:
        config = get_config()
        print(f"Config Loader: {len(config.symbols)} symbols configured")
    except Exception as e:
        print(f"Config Loader failed: {e}")
        return False

    # Test 2: Symbol parser
    try:
        parser = SymbolParser(csv_file_path=CSV_FILE_PATH)
        test_symbol = "NSE:NIFTY25JUL57000CE"
        parsed = parser.parse_symbol_from_nse_format(test_symbol)
        if parsed:
            print(f"Symbol Parser: {test_symbol} -> {parsed.underlying} {parsed.strike} {parsed.option_type}")
        else:
            print(f"Symbol Parser: Failed to parse {test_symbol}")
    except Exception as e:
        print(f"Symbol Parser failed: {e}")
        return False

    # Test 3: Report generator
    try:
        report_gen = ReportGenerator()
        print(f"Report Generator: Output dir ready at {report_gen.output_dir}")
    except Exception as e:
        print(f"Report Generator failed: {e}")
        return False

    print("All individual components working correctly")
    return True

if __name__ == "__main__":
    print("INDEX SCANNER TEST SUITE")
    print("=" * 60)
    
    # Test individual components first
    if not test_individual_components():
        exit(1)
    
    # Test complete flow
    if not test_complete_flow():
        exit(1)
    
    print("\nALL TESTS PASSED!")
    print("The Index Scanner is ready for use with real Fyers API data.")
