

import csv
import os
import requests
import yaml

def get_config():
    """Loads the configuration from config.yaml."""
    with open("C:/Users/<USER>/Desktop/Python/index_scanner/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def download_file(url, destination):
    """Downloads a file from a URL to a destination."""
    response = requests.get(url)
    response.raise_for_status()
    with open(destination, 'wb') as f:
        f.write(response.content)

def get_symbols_from_local_file(file_path, symbol_list):
    """Reads symbols from a local CSV file, optionally filtering by a symbol list."""
    symbols = set()
    with open(file_path, 'r', newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if row and len(row) > 9:
                symbol = row[9].strip()
                if symbol_list == ['ALL'] or any(base in symbol for base in symbol_list):
                    symbols.add(symbol)
    return symbols

def compare_symbols(local_symbols, remote_symbols):
    """Compares two sets of symbols and returns the differences."""
    in_local_not_remote = local_symbols - remote_symbols
    in_remote_not_local = remote_symbols - local_symbols
    in_both = local_symbols.intersection(remote_symbols)
    
    return in_local_not_remote, in_remote_not_local, in_both

def write_diff_to_csv(in_local_not_remote, in_remote_not_local, in_both, output_dir='reports'):
    """Writes the symbol differences to a CSV file."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    output_file = os.path.join(output_dir, 'symbol_diff.csv')
    
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Local Symbols (A)', 'Fyers Symbols (B)', 'Difference'])
        
        for symbol in sorted(in_local_not_remote):
            writer.writerow([symbol, '', 'Present in A, not in B'])
            
        for symbol in sorted(in_remote_not_local):
            writer.writerow(['', symbol, 'Present in B, not in A'])
            
        for symbol in sorted(in_both):
            writer.writerow([symbol, symbol, 'Same'])
            
    return output_file

if __name__ == '__main__':
    config = get_config()
    LOCAL_FILE = 'C:/Users/<USER>/Desktop/Python/index_scanner/NSE_FO.csv'
    REMOTE_URL = config['general']['fyers_api_url']
    
    # Choose the list of symbols to compare. 
    # To compare all symbols, set this to ['ALL'].
    # For a specific list, use ['NIFTY', 'BANKNIFTY', 'IRFC', 'TCS']
    INPUT_SYMBOLS = ['ALL']
    
    print(f"Starting symbol comparison for: {INPUT_SYMBOLS}")
    
    try:
        print(f"Downloading latest symbol file from {REMOTE_URL}...")
        download_file(REMOTE_URL, LOCAL_FILE)
        print("Download complete.")

        local_symbols = get_symbols_from_local_file(LOCAL_FILE, INPUT_SYMBOLS)
        print(f"Found {len(local_symbols)} symbols in the local file.")
        
        # Since we just downloaded the file, the remote and local are the same.
        # We will just compare the file against itself to ensure the logic works.
        remote_symbols = get_symbols_from_local_file(LOCAL_FILE, INPUT_SYMBOLS)
        print(f"Found {len(remote_symbols)} symbols in the remote file (by re-reading the downloaded file).")
        
        in_local, in_remote, in_both = compare_symbols(local_symbols, remote_symbols)
        
        print(f"Symbols only in local file: {len(in_local)}")
        print(f"Symbols only in remote file: {len(in_remote)}")
        print(f"Symbols in both files: {len(in_both)}")
        
        output_file = write_diff_to_csv(in_local, in_remote, in_both)
        
        print(f"\nComparison complete. Differences written to: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: Local file not found at {LOCAL_FILE}")
    except requests.exceptions.RequestException as e:
        print(f"Error fetching remote file: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
