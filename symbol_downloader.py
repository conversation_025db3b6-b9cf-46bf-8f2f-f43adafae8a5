import os
import requests
import yaml
from datetime import datetime

def get_config():
    """Loads the configuration from config.yaml."""
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml"), 'r') as f:
        return yaml.safe_load(f)

def download_file(url, destination):
    """Downloads a file from a URL to a destination."""
    response = requests.get(url)
    response.raise_for_status()
    with open(destination, 'wb') as f:
        f.write(response.content)

def main():
    """Main function to download and backup the symbol file."""
    config = get_config()
    local_file_path = 'NSE_FO.csv'
    mastersymbol_dir = 'mastersymbol'
    remote_url = config['general']['fyers_api_url']

    # Create or clear the mastersymbol directory
    if not os.path.exists(mastersymbol_dir):
        os.makedirs(mastersymbol_dir)
    else:
        for f in os.listdir(mastersymbol_dir):
            os.remove(os.path.join(mastersymbol_dir, f))

    # Backup the existing file
    if os.path.exists(local_file_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(mastersymbol_dir, f"NSE_FO_{timestamp}.csv")
        os.rename(local_file_path, backup_path)
        print(f"Backed up existing file to {backup_path}")

    # Download the new file
    print(f"Downloading latest symbol file from {remote_url}...")
    download_file(remote_url, local_file_path)
    print(f"Downloaded and saved new symbol file to {local_file_path}")

if __name__ == '__main__':
    main()