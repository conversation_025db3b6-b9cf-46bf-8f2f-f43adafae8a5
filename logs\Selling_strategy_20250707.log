root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER TEST WITH LIMITED SYMBOLS
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-07 00:41:04
fyers_config - INFO - Symbol limit: 500
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['ALL']
fyers_config - INFO - Volume filter: >= 10
fyers_config - INFO - LTP filter: 500.0 - 10000.0
fyers_config - INFO - Initializing Index Scanner...
symbol_parser - INFO - Extracted 228 unique underlying symbols from CSV
symbol_parser - INFO - 'ALL' detected - processing all available symbols: 228 unique underlyings found
symbol_parser - INFO - Available underlyings: 360ONE, AARTIIND, ABB, ABCAPITAL, ABFRL, ACC, ADANIENSOL, ADANIENT, ADANIGREEN, ADANIPORTS, ALKEM, AMBER, AMBUJACEM, ANGELONE, APLAPOLLO, APOLLOHOSP, ASHOKLEY, ASIANPAINT, ASTRAL, ATGL, AUBANK, AUROPHARMA, AXISBANK, BAJAJFINSV, BAJFINANCE, BALKRISIND, BANDHANBNK, BANKBARODA, BANKINDIA, BANKNIFTY, BDL, BEL, BHARATFORG, BHARTIARTL, BHEL, BIOCON, BLUESTARCO, BOSCHLTD, BPCL, BRITANNIA, BSE, BSOFT, CAMS, CANBK, CDSL, CESC, CGPOWER, CHAMBLFERT, CHOLAFIN, CIPLA, COALINDIA, COFORGE, COLPAL, CONCOR, CROMPTON, CUMMINSIND, CYIENT, DABUR, DALBHARAT, DELHIVERY, DIVISLAB, DIXON, DLF, DMART, DRREDDY, EICHERMOT, ETERNAL, EXIDEIND, FEDERALBNK, FINNIFTY, FORTIS, GAIL, GLENMARK, GMRAIRPORT, GODREJCP, GODREJPROP, GRANULES, GRASIM, HAL, HAVELLS, HCLTECH, HDFCAMC, HDFCBANK, HDFCLIFE, HEROMOTOCO, HFCL, HINDALCO, HINDCOPPER, HINDPETRO, HINDUNILVR, HINDZINC, HUDCO, ICICIBANK, ICICIGI, ICICIPRULI, IDEA, IDFCFIRSTB, IEX, IGL, IIFL, INDHOTEL, INDIANB, INDIGO, INDUSINDBK, INDUSTOWER, INFY, INOXWIND, IOC, IRB, IRCTC, IREDA, IRFC, ITC, JINDALSTEL, JIOFIN, JSL, JSWENERGY, JSWSTEEL, JUBLFOOD, KALYANKJIL, KAYNES, KEI, KFINTECH, KOTAKBANK, KPITTECH, LAURUSLABS, LICHSGFIN, LICI, LODHA, LT, LTF, LTIM, LUPIN, M&M, M&MFIN, MANAPPURAM, MANKIND, MARICO, MARUTI, MAXHEALTH, MAZDOCK, MCX, MFSL, MGL, MIDCPNIFTY, MOTHERSON, MPHASIS, MUTHOOTFIN, NATIONALUM, NAUKRI, NBCC, NCC, NESTLEIND, NHPC, NIFTY, NIFTYNXT50, NMDC, NTPC, NYKAA, OBEROIRLTY, OFSS, OIL, ONGC, PAGEIND, PATANJALI, PAYTM, PEL, PERSISTENT, PETRONET, PFC, PGEL, PHOENIXLTD, PIDILITIND, PIIND, PNB, PNBHOUSING, POLICYBZR, POLYCAB, POONAWALLA, POWERGRID, PPLPHARMA, PRESTIGE, RBLBANK, RECLTD, RELIANCE, RVNL, SAIL, SBICARD, SBILIFE, SBIN, SHREECEM, SHRIRAMFIN, SIEMENS, SJVN, SOLARINDS, SONACOMS, SRF, SUNPHARMA, SUPREMEIND, SYNGENE, TATACHEM, TATACOMM, TATACONSUM, TATAELXSI, TATAMOTORS, TATAPOWER, TATASTEEL, TATATECH, TCS, TECHM, TIINDIA, TITAGARH, TITAN, TORNTPHARM, TORNTPOWER, TRENT, TVSMOTOR, ULTRACEMCO, UNIONBANK, UNITDSPR, UNOMINDA, UPL, VBL, VEDL, VOLTAS, WIPRO, YESBANK, ZYDUSLIFE
symbol_parser - INFO - Using optimized general pattern for 228 symbols
fyers_config - INFO - Testing symbol loading with limit of 500...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Reached symbol limit of 500, stopping processing
symbol_parser - INFO - Performance stats: Processed 1876 rows, found 500 valid symbols, filtered to 500 symbols for target months ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Filtered to 0 symbols for underlyings: ['ALL']
symbol_parser - INFO - Prepared 0 symbols for scanning
fyers_config - WARNING - No symbols found for scanning
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER TEST WITH LIMITED SYMBOLS
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-07 00:42:05
fyers_config - INFO - Symbol limit: 500
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['ALL']
fyers_config - INFO - Volume filter: >= 10
fyers_config - INFO - LTP filter: 500.0 - 10000.0
fyers_config - INFO - Initializing Index Scanner...
symbol_parser - INFO - Extracted 228 unique underlying symbols from CSV
symbol_parser - INFO - 'ALL' detected - processing all available symbols: 228 unique underlyings found
symbol_parser - INFO - Available underlyings: 360ONE, AARTIIND, ABB, ABCAPITAL, ABFRL, ACC, ADANIENSOL, ADANIENT, ADANIGREEN, ADANIPORTS, ALKEM, AMBER, AMBUJACEM, ANGELONE, APLAPOLLO, APOLLOHOSP, ASHOKLEY, ASIANPAINT, ASTRAL, ATGL, AUBANK, AUROPHARMA, AXISBANK, BAJAJFINSV, BAJFINANCE, BALKRISIND, BANDHANBNK, BANKBARODA, BANKINDIA, BANKNIFTY, BDL, BEL, BHARATFORG, BHARTIARTL, BHEL, BIOCON, BLUESTARCO, BOSCHLTD, BPCL, BRITANNIA, BSE, BSOFT, CAMS, CANBK, CDSL, CESC, CGPOWER, CHAMBLFERT, CHOLAFIN, CIPLA, COALINDIA, COFORGE, COLPAL, CONCOR, CROMPTON, CUMMINSIND, CYIENT, DABUR, DALBHARAT, DELHIVERY, DIVISLAB, DIXON, DLF, DMART, DRREDDY, EICHERMOT, ETERNAL, EXIDEIND, FEDERALBNK, FINNIFTY, FORTIS, GAIL, GLENMARK, GMRAIRPORT, GODREJCP, GODREJPROP, GRANULES, GRASIM, HAL, HAVELLS, HCLTECH, HDFCAMC, HDFCBANK, HDFCLIFE, HEROMOTOCO, HFCL, HINDALCO, HINDCOPPER, HINDPETRO, HINDUNILVR, HINDZINC, HUDCO, ICICIBANK, ICICIGI, ICICIPRULI, IDEA, IDFCFIRSTB, IEX, IGL, IIFL, INDHOTEL, INDIANB, INDIGO, INDUSINDBK, INDUSTOWER, INFY, INOXWIND, IOC, IRB, IRCTC, IREDA, IRFC, ITC, JINDALSTEL, JIOFIN, JSL, JSWENERGY, JSWSTEEL, JUBLFOOD, KALYANKJIL, KAYNES, KEI, KFINTECH, KOTAKBANK, KPITTECH, LAURUSLABS, LICHSGFIN, LICI, LODHA, LT, LTF, LTIM, LUPIN, M&M, M&MFIN, MANAPPURAM, MANKIND, MARICO, MARUTI, MAXHEALTH, MAZDOCK, MCX, MFSL, MGL, MIDCPNIFTY, MOTHERSON, MPHASIS, MUTHOOTFIN, NATIONALUM, NAUKRI, NBCC, NCC, NESTLEIND, NHPC, NIFTY, NIFTYNXT50, NMDC, NTPC, NYKAA, OBEROIRLTY, OFSS, OIL, ONGC, PAGEIND, PATANJALI, PAYTM, PEL, PERSISTENT, PETRONET, PFC, PGEL, PHOENIXLTD, PIDILITIND, PIIND, PNB, PNBHOUSING, POLICYBZR, POLYCAB, POONAWALLA, POWERGRID, PPLPHARMA, PRESTIGE, RBLBANK, RECLTD, RELIANCE, RVNL, SAIL, SBICARD, SBILIFE, SBIN, SHREECEM, SHRIRAMFIN, SIEMENS, SJVN, SOLARINDS, SONACOMS, SRF, SUNPHARMA, SUPREMEIND, SYNGENE, TATACHEM, TATACOMM, TATACONSUM, TATAELXSI, TATAMOTORS, TATAPOWER, TATASTEEL, TATATECH, TCS, TECHM, TIINDIA, TITAGARH, TITAN, TORNTPHARM, TORNTPOWER, TRENT, TVSMOTOR, ULTRACEMCO, UNIONBANK, UNITDSPR, UNOMINDA, UPL, VBL, VEDL, VOLTAS, WIPRO, YESBANK, ZYDUSLIFE
symbol_parser - INFO - Using optimized general pattern for 228 symbols
fyers_config - INFO - Testing symbol loading with limit of 500...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Reached symbol limit of 500, stopping processing
symbol_parser - INFO - Performance stats: Processed 1876 rows, found 500 valid symbols, filtered to 500 symbols for target months ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Filtered to 500 symbols for underlyings: ['IRCTC', 'DIXON', 'MCX', 'MANKIND', 'IRB', 'EICHERMOT', 'ASTRAL', 'MAXHEALTH', 'APOLLOHOSP', 'ASIANPAINT', 'BLUESTARCO', 'SIEMENS', 'PPLPHARMA', 'MARUTI', 'RECLTD', 'UNITDSPR', 'CHAMBLFERT', 'SOLARINDS', 'MANAPPURAM', 'SRF', 'PGEL', 'BHEL', 'VOLTAS', 'INDHOTEL', 'JSL', 'ONGC', '360ONE', 'CAMS', 'SUNPHARMA', 'ULTRACEMCO', 'BANKINDIA', 'KEI', 'PAYTM', 'HDFCBANK', 'NBCC', 'BIOCON', 'BSOFT', 'IIFL', 'BANKNIFTY', 'OIL', 'AXISBANK', 'CONCOR', 'HDFCAMC', 'HINDUNILVR', 'PATANJALI', 'HINDALCO', 'CGPOWER', 'TATAPOWER', 'LAURUSLABS', 'AUROPHARMA', 'GRASIM', 'TIINDIA', 'BSE', 'ADANIENSOL', 'YESBANK', 'MIDCPNIFTY', 'CYIENT', 'BAJAJFINSV', 'POLYCAB', 'IREDA', 'BANDHANBNK', 'POWERGRID', 'ABCAPITAL', 'LODHA', 'NIFTYNXT50', 'DELHIVERY', 'ITC', 'ASHOKLEY', 'INDIANB', 'SYNGENE', 'POONAWALLA', 'INOXWIND', 'ICICIBANK', 'NAUKRI', 'VEDL', 'ACC', 'NESTLEIND', 'TORNTPHARM', 'CDSL', 'ATGL', 'HDFCLIFE', 'NHPC', 'SBICARD', 'INDUSINDBK', 'MFSL', 'BOSCHLTD', 'DMART', 'LICHSGFIN', 'HCLTECH', 'HUDCO', 'LICI', 'UNIONBANK', 'GLENMARK', 'FEDERALBNK', 'TECHM', 'TVSMOTOR', 'DRREDDY', 'INDUSTOWER', 'MAZDOCK', 'NYKAA', 'UNOMINDA', 'AUBANK', 'COALINDIA', 'GRANULES', 'TATACONSUM', 'FINNIFTY', 'HINDZINC', 'PIDILITIND', 'KALYANKJIL', 'LUPIN', 'GMRAIRPORT', 'NMDC', 'DLF', 'SHRIRAMFIN', 'BANKBARODA', 'TCS', 'BPCL', 'HAL', 'SAIL', 'INDIGO', 'GAIL', 'LTIM', 'BAJFINANCE', 'M&M', 'JIOFIN', 'AMBUJACEM', 'M&MFIN', 'ABFRL', 'APLAPOLLO', 'JSWSTEEL', 'ADANIENT', 'SUPREMEIND', 'KOTAKBANK', 'COFORGE', 'SONACOMS', 'PFC', 'IEX', 'RVNL', 'PNB', 'KPITTECH', 'FORTIS', 'PERSISTENT', 'LT', 'GODREJPROP', 'HINDPETRO', 'JINDALSTEL', 'KAYNES', 'ETERNAL', 'CANBK', 'IDFCFIRSTB', 'TATACOMM', 'BHARTIARTL', 'TATAELXSI', 'OFSS', 'PAGEIND', 'NATIONALUM', 'SJVN', 'TITAN', 'MUTHOOTFIN', 'IRFC', 'PETRONET', 'WIPRO', 'DALBHARAT', 'CESC', 'ALKEM', 'MGL', 'INFY', 'KFINTECH', 'CROMPTON', 'HFCL', 'LTF', 'ADANIGREEN', 'NTPC', 'MARICO', 'NCC', 'HEROMOTOCO', 'PEL', 'BDL', 'AARTIIND', 'TORNTPOWER', 'BALKRISIND', 'EXIDEIND', 'TATASTEEL', 'PHOENIXLTD', 'NIFTY', 'ANGELONE', 'MOTHERSON', 'ICICIPRULI', 'COLPAL', 'TITAGARH', 'MPHASIS', 'JSWENERGY', 'SHREECEM', 'CHOLAFIN', 'GODREJCP', 'AMBER', 'SBILIFE', 'IOC', 'BRITANNIA', 'ICICIGI', 'TRENT', 'BEL', 'HINDCOPPER', 'RBLBANK', 'ZYDUSLIFE', 'TATAMOTORS', 'ADANIPORTS', 'IGL', 'TATACHEM', 'HAVELLS', 'PRESTIGE', 'DABUR', 'CUMMINSIND', 'VBL', 'BHARATFORG', 'PNBHOUSING', 'ABB', 'CIPLA', 'IDEA', 'POLICYBZR', 'DIVISLAB', 'UPL', 'SBIN', 'JUBLFOOD', 'OBEROIRLTY', 'PIIND', 'TATATECH', 'RELIANCE']
symbol_parser - INFO - Prepared 500 symbols for scanning
fyers_config - INFO - Successfully loaded 500 symbols (limited to 500)
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Reached symbol limit of 500, stopping processing
symbol_parser - INFO - Performance stats: Processed 1876 rows, found 500 valid symbols, filtered to 500 symbols for target months ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Filtered to 500 symbols for underlyings: ['IRCTC', 'DIXON', 'MCX', 'MANKIND', 'IRB', 'EICHERMOT', 'ASTRAL', 'MAXHEALTH', 'APOLLOHOSP', 'ASIANPAINT', 'BLUESTARCO', 'SIEMENS', 'PPLPHARMA', 'MARUTI', 'RECLTD', 'UNITDSPR', 'CHAMBLFERT', 'SOLARINDS', 'MANAPPURAM', 'SRF', 'PGEL', 'BHEL', 'VOLTAS', 'INDHOTEL', 'JSL', 'ONGC', '360ONE', 'CAMS', 'SUNPHARMA', 'ULTRACEMCO', 'BANKINDIA', 'KEI', 'PAYTM', 'HDFCBANK', 'NBCC', 'BIOCON', 'BSOFT', 'IIFL', 'BANKNIFTY', 'OIL', 'AXISBANK', 'CONCOR', 'HDFCAMC', 'HINDUNILVR', 'PATANJALI', 'HINDALCO', 'CGPOWER', 'TATAPOWER', 'LAURUSLABS', 'AUROPHARMA', 'GRASIM', 'TIINDIA', 'BSE', 'ADANIENSOL', 'YESBANK', 'MIDCPNIFTY', 'CYIENT', 'BAJAJFINSV', 'POLYCAB', 'IREDA', 'BANDHANBNK', 'POWERGRID', 'ABCAPITAL', 'LODHA', 'NIFTYNXT50', 'DELHIVERY', 'ITC', 'ASHOKLEY', 'INDIANB', 'SYNGENE', 'POONAWALLA', 'INOXWIND', 'ICICIBANK', 'NAUKRI', 'VEDL', 'ACC', 'NESTLEIND', 'TORNTPHARM', 'CDSL', 'ATGL', 'HDFCLIFE', 'NHPC', 'SBICARD', 'INDUSINDBK', 'MFSL', 'BOSCHLTD', 'DMART', 'LICHSGFIN', 'HCLTECH', 'HUDCO', 'LICI', 'UNIONBANK', 'GLENMARK', 'FEDERALBNK', 'TECHM', 'TVSMOTOR', 'DRREDDY', 'INDUSTOWER', 'MAZDOCK', 'NYKAA', 'UNOMINDA', 'AUBANK', 'COALINDIA', 'GRANULES', 'TATACONSUM', 'FINNIFTY', 'HINDZINC', 'PIDILITIND', 'KALYANKJIL', 'LUPIN', 'GMRAIRPORT', 'NMDC', 'DLF', 'SHRIRAMFIN', 'BANKBARODA', 'TCS', 'BPCL', 'HAL', 'SAIL', 'INDIGO', 'GAIL', 'LTIM', 'BAJFINANCE', 'M&M', 'JIOFIN', 'AMBUJACEM', 'M&MFIN', 'ABFRL', 'APLAPOLLO', 'JSWSTEEL', 'ADANIENT', 'SUPREMEIND', 'KOTAKBANK', 'COFORGE', 'SONACOMS', 'PFC', 'IEX', 'RVNL', 'PNB', 'KPITTECH', 'FORTIS', 'PERSISTENT', 'LT', 'GODREJPROP', 'HINDPETRO', 'JINDALSTEL', 'KAYNES', 'ETERNAL', 'CANBK', 'IDFCFIRSTB', 'TATACOMM', 'BHARTIARTL', 'TATAELXSI', 'OFSS', 'PAGEIND', 'NATIONALUM', 'SJVN', 'TITAN', 'MUTHOOTFIN', 'IRFC', 'PETRONET', 'WIPRO', 'DALBHARAT', 'CESC', 'ALKEM', 'MGL', 'INFY', 'KFINTECH', 'CROMPTON', 'HFCL', 'LTF', 'ADANIGREEN', 'NTPC', 'MARICO', 'NCC', 'HEROMOTOCO', 'PEL', 'BDL', 'AARTIIND', 'TORNTPOWER', 'BALKRISIND', 'EXIDEIND', 'TATASTEEL', 'PHOENIXLTD', 'NIFTY', 'ANGELONE', 'MOTHERSON', 'ICICIPRULI', 'COLPAL', 'TITAGARH', 'MPHASIS', 'JSWENERGY', 'SHREECEM', 'CHOLAFIN', 'GODREJCP', 'AMBER', 'SBILIFE', 'IOC', 'BRITANNIA', 'ICICIGI', 'TRENT', 'BEL', 'HINDCOPPER', 'RBLBANK', 'ZYDUSLIFE', 'TATAMOTORS', 'ADANIPORTS', 'IGL', 'TATACHEM', 'HAVELLS', 'PRESTIGE', 'DABUR', 'CUMMINSIND', 'VBL', 'BHARATFORG', 'PNBHOUSING', 'ABB', 'CIPLA', 'IDEA', 'POLICYBZR', 'DIVISLAB', 'UPL', 'SBIN', 'JUBLFOOD', 'OBEROIRLTY', 'PIIND', 'TATATECH', 'RELIANCE']
fyers_config - INFO - Successfully parsed 500 symbol objects
fyers_config - INFO - Testing CSV generation...
symbol_parser - INFO - Saved 500 filtered symbols to reports\test_filtered_symbols.csv
fyers_config - INFO - Sample symbols loaded:
fyers_config - INFO -   1. NSE:HFCL25JUL65CE
fyers_config - INFO -   2. NSE:HFCL25JUL65PE
fyers_config - INFO -   3. NSE:IOC25JUL85CE
fyers_config - INFO -   4. NSE:IOC25JUL85PE
fyers_config - INFO -   5. NSE:IOC25JUL95CE
fyers_config - INFO -   6. NSE:IOC25JUL95PE
fyers_config - INFO -   7. NSE:HFCL25JUL75CE
fyers_config - INFO -   8. NSE:HFCL25JUL75PE
fyers_config - INFO -   9. NSE:HFCL25JUL77PE
fyers_config - INFO -   10. NSE:HFCL25JUL79PE
fyers_config - INFO -   ... and 490 more symbols
fyers_config - INFO - Underlying symbol distribution:
fyers_config - INFO -   HFCL: 25 symbols
fyers_config - INFO -   HINDALCO: 40 symbols
fyers_config - INFO -   HINDCOPPER: 22 symbols
fyers_config - INFO -   HINDPETRO: 48 symbols
fyers_config - INFO -   HINDUNILVR: 82 symbols
fyers_config - INFO -   IDFCFIRSTB: 44 symbols
fyers_config - INFO -   IEX: 46 symbols
fyers_config - INFO -   IGL: 50 symbols
fyers_config - INFO -   IIFL: 34 symbols
fyers_config - INFO -   INDHOTEL: 43 symbols
fyers_config - INFO -   IOC: 48 symbols
fyers_config - INFO -   IRB: 8 symbols
fyers_config - INFO -   IRCTC: 10 symbols
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER TEST COMPLETED SUCCESSFULLY
fyers_config - INFO - ================================================================================
fyers_config - INFO - End time: 2025-07-07 00:42:06
fyers_config - INFO - Total symbols processed: 500
fyers_config - INFO - Total unique underlyings: 13
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-07 00:46:28
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['ALL']
fyers_config - INFO - Volume filter: >= 10
fyers_config - INFO - LTP filter: 2500.0 - 100000.0
fyers_config - INFO - Initializing Index Scanner...
symbol_parser - INFO - Extracted 228 unique underlying symbols from CSV
symbol_parser - INFO - 'ALL' detected - processing all available symbols: 228 unique underlyings found
symbol_parser - INFO - Available underlyings: 360ONE, AARTIIND, ABB, ABCAPITAL, ABFRL, ACC, ADANIENSOL, ADANIENT, ADANIGREEN, ADANIPORTS, ALKEM, AMBER, AMBUJACEM, ANGELONE, APLAPOLLO, APOLLOHOSP, ASHOKLEY, ASIANPAINT, ASTRAL, ATGL, AUBANK, AUROPHARMA, AXISBANK, BAJAJFINSV, BAJFINANCE, BALKRISIND, BANDHANBNK, BANKBARODA, BANKINDIA, BANKNIFTY, BDL, BEL, BHARATFORG, BHARTIARTL, BHEL, BIOCON, BLUESTARCO, BOSCHLTD, BPCL, BRITANNIA, BSE, BSOFT, CAMS, CANBK, CDSL, CESC, CGPOWER, CHAMBLFERT, CHOLAFIN, CIPLA, COALINDIA, COFORGE, COLPAL, CONCOR, CROMPTON, CUMMINSIND, CYIENT, DABUR, DALBHARAT, DELHIVERY, DIVISLAB, DIXON, DLF, DMART, DRREDDY, EICHERMOT, ETERNAL, EXIDEIND, FEDERALBNK, FINNIFTY, FORTIS, GAIL, GLENMARK, GMRAIRPORT, GODREJCP, GODREJPROP, GRANULES, GRASIM, HAL, HAVELLS, HCLTECH, HDFCAMC, HDFCBANK, HDFCLIFE, HEROMOTOCO, HFCL, HINDALCO, HINDCOPPER, HINDPETRO, HINDUNILVR, HINDZINC, HUDCO, ICICIBANK, ICICIGI, ICICIPRULI, IDEA, IDFCFIRSTB, IEX, IGL, IIFL, INDHOTEL, INDIANB, INDIGO, INDUSINDBK, INDUSTOWER, INFY, INOXWIND, IOC, IRB, IRCTC, IREDA, IRFC, ITC, JINDALSTEL, JIOFIN, JSL, JSWENERGY, JSWSTEEL, JUBLFOOD, KALYANKJIL, KAYNES, KEI, KFINTECH, KOTAKBANK, KPITTECH, LAURUSLABS, LICHSGFIN, LICI, LODHA, LT, LTF, LTIM, LUPIN, M&M, M&MFIN, MANAPPURAM, MANKIND, MARICO, MARUTI, MAXHEALTH, MAZDOCK, MCX, MFSL, MGL, MIDCPNIFTY, MOTHERSON, MPHASIS, MUTHOOTFIN, NATIONALUM, NAUKRI, NBCC, NCC, NESTLEIND, NHPC, NIFTY, NIFTYNXT50, NMDC, NTPC, NYKAA, OBEROIRLTY, OFSS, OIL, ONGC, PAGEIND, PATANJALI, PAYTM, PEL, PERSISTENT, PETRONET, PFC, PGEL, PHOENIXLTD, PIDILITIND, PIIND, PNB, PNBHOUSING, POLICYBZR, POLYCAB, POONAWALLA, POWERGRID, PPLPHARMA, PRESTIGE, RBLBANK, RECLTD, RELIANCE, RVNL, SAIL, SBICARD, SBILIFE, SBIN, SHREECEM, SHRIRAMFIN, SIEMENS, SJVN, SOLARINDS, SONACOMS, SRF, SUNPHARMA, SUPREMEIND, SYNGENE, TATACHEM, TATACOMM, TATACONSUM, TATAELXSI, TATAMOTORS, TATAPOWER, TATASTEEL, TATATECH, TCS, TECHM, TIINDIA, TITAGARH, TITAN, TORNTPHARM, TORNTPOWER, TRENT, TVSMOTOR, ULTRACEMCO, UNIONBANK, UNITDSPR, UNOMINDA, UPL, VBL, VEDL, VOLTAS, WIPRO, YESBANK, ZYDUSLIFE
symbol_parser - INFO - Using optimized general pattern for 228 symbols
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\index_scanner
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Processed 1000 valid symbols so far...
symbol_parser - INFO - Processed 2000 valid symbols so far...
symbol_parser - INFO - Processed 3000 valid symbols so far...
symbol_parser - INFO - Processed 4000 valid symbols so far...
symbol_parser - INFO - Processed 5000 valid symbols so far...
symbol_parser - INFO - Processed 6000 valid symbols so far...
symbol_parser - INFO - Processed 7000 valid symbols so far...
symbol_parser - INFO - Processed 8000 valid symbols so far...
symbol_parser - INFO - Processed 9000 valid symbols so far...
symbol_parser - INFO - Processed 10000 valid symbols so far...
symbol_parser - INFO - Processed 11000 valid symbols so far...
symbol_parser - INFO - Processed 12000 valid symbols so far...
symbol_parser - INFO - Processed 13000 valid symbols so far...
symbol_parser - INFO - Processed 14000 valid symbols so far...
symbol_parser - INFO - Processed 15000 valid symbols so far...
symbol_parser - INFO - Processed 16000 valid symbols so far...
symbol_parser - INFO - Processed 17000 valid symbols so far...
symbol_parser - INFO - Processed 18000 valid symbols so far...
symbol_parser - INFO - Processed 19000 valid symbols so far...
symbol_parser - INFO - Processed 20000 valid symbols so far...
symbol_parser - INFO - Processed 21000 valid symbols so far...
symbol_parser - INFO - Processed 22000 valid symbols so far...
symbol_parser - INFO - Processed 23000 valid symbols so far...
symbol_parser - INFO - Processed 24000 valid symbols so far...
symbol_parser - INFO - Processed 25000 valid symbols so far...
symbol_parser - INFO - Processed 26000 valid symbols so far...
symbol_parser - INFO - Processed 27000 valid symbols so far...
symbol_parser - INFO - Processed 28000 valid symbols so far...
symbol_parser - INFO - Processed 29000 valid symbols so far...
symbol_parser - INFO - Processed 30000 valid symbols so far...
symbol_parser - INFO - Processed 31000 valid symbols so far...
symbol_parser - INFO - Processed 32000 valid symbols so far...
symbol_parser - INFO - Processed 33000 valid symbols so far...
symbol_parser - INFO - Processed 34000 valid symbols so far...
symbol_parser - INFO - Processed 35000 valid symbols so far...
symbol_parser - INFO - Processed 36000 valid symbols so far...
symbol_parser - INFO - Processed 37000 valid symbols so far...
symbol_parser - INFO - Processed 38000 valid symbols so far...
symbol_parser - INFO - Processed 39000 valid symbols so far...
symbol_parser - INFO - Processed 40000 valid symbols so far...
symbol_parser - INFO - Processed 41000 valid symbols so far...
symbol_parser - INFO - Processed 42000 valid symbols so far...
symbol_parser - INFO - Processed 43000 valid symbols so far...
symbol_parser - INFO - Processed 44000 valid symbols so far...
symbol_parser - INFO - Processed 45000 valid symbols so far...
symbol_parser - INFO - Processed 46000 valid symbols so far...
symbol_parser - INFO - Processed 47000 valid symbols so far...
symbol_parser - INFO - Processed 48000 valid symbols so far...
symbol_parser - INFO - Processed 49000 valid symbols so far...
symbol_parser - INFO - Processed 50000 valid symbols so far...
symbol_parser - INFO - Processed 51000 valid symbols so far...
symbol_parser - INFO - Processed 52000 valid symbols so far...
symbol_parser - INFO - Processed 53000 valid symbols so far...
symbol_parser - INFO - Processed 54000 valid symbols so far...
symbol_parser - INFO - Processed 55000 valid symbols so far...
symbol_parser - INFO - Processed 56000 valid symbols so far...
symbol_parser - INFO - Processed 57000 valid symbols so far...
symbol_parser - INFO - Processed 58000 valid symbols so far...
symbol_parser - INFO - Processed 59000 valid symbols so far...
symbol_parser - INFO - Processed 60000 valid symbols so far...
symbol_parser - INFO - Processed 61000 valid symbols so far...
symbol_parser - INFO - Processed 62000 valid symbols so far...
symbol_parser - INFO - Processed 63000 valid symbols so far...
symbol_parser - INFO - Processed 64000 valid symbols so far...
symbol_parser - INFO - Processed 65000 valid symbols so far...
symbol_parser - INFO - Processed 66000 valid symbols so far...
symbol_parser - INFO - Processed 67000 valid symbols so far...
symbol_parser - INFO - Processed 68000 valid symbols so far...
symbol_parser - INFO - Processed 69000 valid symbols so far...
symbol_parser - INFO - Processed 70000 valid symbols so far...
symbol_parser - INFO - Processed 71000 valid symbols so far...
symbol_parser - INFO - Processed 72000 valid symbols so far...
symbol_parser - INFO - Performance stats: Processed 76092 rows, found 73274 valid symbols, filtered to 72382 symbols for target months ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Filtered to 72382 symbols for underlyings: ['HAL', 'BAJFINANCE', 'BIOCON', 'PNB', 'ADANIENT', 'TATACOMM', 'IIFL', 'SUPREMEIND', 'ULTRACEMCO', 'IRCTC', 'IREDA', 'IEX', 'ICICIPRULI', 'IDEA', 'INDIANB', 'NHPC', 'TATASTEEL', 'HDFCAMC', 'INDIGO', 'LTF', 'BHARATFORG', 'LICI', 'BANDHANBNK', 'WIPRO', 'HDFCLIFE', 'KPITTECH', 'GRANULES', 'FEDERALBNK', 'SBICARD', 'BRITANNIA', 'HCLTECH', 'SYNGENE', '360ONE', 'JSWENERGY', 'KOTAKBANK', 'BANKINDIA', 'SJVN', 'HUDCO', 'HAVELLS', 'POONAWALLA', 'IDFCFIRSTB', 'TATAELXSI', 'AXISBANK', 'IRFC', 'OFSS', 'AUBANK', 'TORNTPOWER', 'SIEMENS', 'UPL', 'VEDL', 'PHOENIXLTD', 'ASTRAL', 'CAMS', 'CROMPTON', 'BPCL', 'HINDUNILVR', 'BANKBARODA', 'AMBUJACEM', 'MFSL', 'PEL', 'NATIONALUM', 'GODREJCP', 'PAYTM', 'APOLLOHOSP', 'TATAMOTORS', 'HINDCOPPER', 'CANBK', 'NMDC', 'MPHASIS', 'DRREDDY', 'TVSMOTOR', 'GRASIM', 'JSL', 'KFINTECH', 'COFORGE', 'BSOFT', 'OBEROIRLTY', 'CESC', 'NBCC', 'HINDPETRO', 'AUROPHARMA', 'MUTHOOTFIN', 'UNITDSPR', 'LT', 'ETERNAL', 'CDSL', 'SRF', 'NIFTYNXT50', 'TITAGARH', 'AARTIIND', 'MANAPPURAM', 'LODHA', 'NCC', 'PNBHOUSING', 'POWERGRID', 'SBIN', 'PATANJALI', 'HEROMOTOCO', 'MAXHEALTH', 'ADANIENSOL', 'ICICIGI', 'RELIANCE', 'ASIANPAINT', 'PETRONET', 'PRESTIGE', 'EICHERMOT', 'MIDCPNIFTY', 'ABB', 'INDHOTEL', 'PFC', 'NIFTY', 'APLAPOLLO', 'MGL', 'PIIND', 'TCS', 'MANKIND', 'JSWSTEEL', 'SAIL', 'VOLTAS', 'BOSCHLTD', 'ABFRL', 'DELHIVERY', 'ABCAPITAL', 'POLICYBZR', 'UNIONBANK', 'GLENMARK', 'GMRAIRPORT', 'DMART', 'MCX', 'INOXWIND', 'ALKEM', 'FINNIFTY', 'BEL', 'ITC', 'CONCOR', 'UNOMINDA', 'BALKRISIND', 'PAGEIND', 'CHOLAFIN', 'POLYCAB', 'ZYDUSLIFE', 'TITAN', 'TATACHEM', 'CIPLA', 'IOC', 'AMBER', 'MOTHERSON', 'TATATECH', 'TORNTPHARM', 'JIOFIN', 'MARUTI', 'ADANIGREEN', 'BHEL', 'DALBHARAT', 'DIVISLAB', 'FORTIS', 'PIDILITIND', 'PGEL', 'SHRIRAMFIN', 'PPLPHARMA', 'JUBLFOOD', 'INDUSTOWER', 'TRENT', 'SONACOMS', 'NTPC', 'GAIL', 'RBLBANK', 'ATGL', 'ANGELONE', 'HINDALCO', 'BHARTIARTL', 'JINDALSTEL', 'PERSISTENT', 'LUPIN', 'HINDZINC', 'CYIENT', 'COALINDIA', 'TATACONSUM', 'NYKAA', 'SBILIFE', 'MARICO', 'CHAMBLFERT', 'BANKNIFTY', 'DLF', 'ADANIPORTS', 'OIL', 'SHREECEM', 'HFCL', 'ASHOKLEY', 'RECLTD', 'IRB', 'TECHM', 'DABUR', 'KALYANKJIL', 'ACC', 'CGPOWER', 'COLPAL', 'BDL', 'KEI', 'LAURUSLABS', 'ONGC', 'LICHSGFIN', 'NESTLEIND', 'RVNL', 'CUMMINSIND', 'SUNPHARMA', 'YESBANK', 'HDFCBANK', 'M&MFIN', 'VBL', 'BAJAJFINSV', 'EXIDEIND', 'GODREJPROP', 'NAUKRI', 'SOLARINDS', 'LTIM', 'DIXON', 'IGL', 'MAZDOCK', 'KAYNES', 'INFY', 'BSE', 'TATAPOWER', 'TIINDIA', 'ICICIBANK', 'INDUSINDBK', 'M&M', 'BLUESTARCO']
symbol_parser - INFO - Prepared 72382 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Processed 1000 valid symbols so far...
symbol_parser - INFO - Processed 2000 valid symbols so far...
symbol_parser - INFO - Processed 3000 valid symbols so far...
symbol_parser - INFO - Processed 4000 valid symbols so far...
symbol_parser - INFO - Processed 5000 valid symbols so far...
symbol_parser - INFO - Processed 6000 valid symbols so far...
symbol_parser - INFO - Processed 7000 valid symbols so far...
symbol_parser - INFO - Processed 8000 valid symbols so far...
symbol_parser - INFO - Processed 9000 valid symbols so far...
symbol_parser - INFO - Processed 10000 valid symbols so far...
symbol_parser - INFO - Processed 11000 valid symbols so far...
symbol_parser - INFO - Processed 12000 valid symbols so far...
symbol_parser - INFO - Processed 13000 valid symbols so far...
symbol_parser - INFO - Processed 14000 valid symbols so far...
symbol_parser - INFO - Processed 15000 valid symbols so far...
symbol_parser - INFO - Processed 16000 valid symbols so far...
symbol_parser - INFO - Processed 17000 valid symbols so far...
symbol_parser - INFO - Processed 18000 valid symbols so far...
symbol_parser - INFO - Processed 19000 valid symbols so far...
symbol_parser - INFO - Processed 20000 valid symbols so far...
symbol_parser - INFO - Processed 21000 valid symbols so far...
symbol_parser - INFO - Processed 22000 valid symbols so far...
symbol_parser - INFO - Processed 23000 valid symbols so far...
symbol_parser - INFO - Processed 24000 valid symbols so far...
symbol_parser - INFO - Processed 25000 valid symbols so far...
symbol_parser - INFO - Processed 26000 valid symbols so far...
symbol_parser - INFO - Processed 27000 valid symbols so far...
symbol_parser - INFO - Processed 28000 valid symbols so far...
symbol_parser - INFO - Processed 29000 valid symbols so far...
symbol_parser - INFO - Processed 30000 valid symbols so far...
symbol_parser - INFO - Processed 31000 valid symbols so far...
symbol_parser - INFO - Processed 32000 valid symbols so far...
symbol_parser - INFO - Processed 33000 valid symbols so far...
symbol_parser - INFO - Processed 34000 valid symbols so far...
symbol_parser - INFO - Processed 35000 valid symbols so far...
symbol_parser - INFO - Processed 36000 valid symbols so far...
symbol_parser - INFO - Processed 37000 valid symbols so far...
symbol_parser - INFO - Processed 38000 valid symbols so far...
symbol_parser - INFO - Processed 39000 valid symbols so far...
symbol_parser - INFO - Processed 40000 valid symbols so far...
symbol_parser - INFO - Processed 41000 valid symbols so far...
symbol_parser - INFO - Processed 42000 valid symbols so far...
symbol_parser - INFO - Processed 43000 valid symbols so far...
symbol_parser - INFO - Processed 44000 valid symbols so far...
symbol_parser - INFO - Processed 45000 valid symbols so far...
symbol_parser - INFO - Processed 46000 valid symbols so far...
symbol_parser - INFO - Processed 47000 valid symbols so far...
symbol_parser - INFO - Processed 48000 valid symbols so far...
symbol_parser - INFO - Processed 49000 valid symbols so far...
symbol_parser - INFO - Processed 50000 valid symbols so far...
symbol_parser - INFO - Processed 51000 valid symbols so far...
symbol_parser - INFO - Processed 52000 valid symbols so far...
symbol_parser - INFO - Processed 53000 valid symbols so far...
symbol_parser - INFO - Processed 54000 valid symbols so far...
symbol_parser - INFO - Processed 55000 valid symbols so far...
symbol_parser - INFO - Processed 56000 valid symbols so far...
symbol_parser - INFO - Processed 57000 valid symbols so far...
symbol_parser - INFO - Processed 58000 valid symbols so far...
symbol_parser - INFO - Processed 59000 valid symbols so far...
symbol_parser - INFO - Processed 60000 valid symbols so far...
symbol_parser - INFO - Processed 61000 valid symbols so far...
symbol_parser - INFO - Processed 62000 valid symbols so far...
symbol_parser - INFO - Processed 63000 valid symbols so far...
symbol_parser - INFO - Processed 64000 valid symbols so far...
symbol_parser - INFO - Processed 65000 valid symbols so far...
symbol_parser - INFO - Processed 66000 valid symbols so far...
symbol_parser - INFO - Processed 67000 valid symbols so far...
symbol_parser - INFO - Processed 68000 valid symbols so far...
symbol_parser - INFO - Processed 69000 valid symbols so far...
symbol_parser - INFO - Processed 70000 valid symbols so far...
symbol_parser - INFO - Processed 71000 valid symbols so far...
symbol_parser - INFO - Processed 72000 valid symbols so far...
symbol_parser - INFO - Performance stats: Processed 76092 rows, found 73274 valid symbols, filtered to 72382 symbols for target months ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Filtered to 72382 symbols for underlyings: ['HAL', 'BAJFINANCE', 'BIOCON', 'PNB', 'ADANIENT', 'TATACOMM', 'IIFL', 'SUPREMEIND', 'ULTRACEMCO', 'IRCTC', 'IREDA', 'IEX', 'ICICIPRULI', 'IDEA', 'INDIANB', 'NHPC', 'TATASTEEL', 'HDFCAMC', 'INDIGO', 'LTF', 'BHARATFORG', 'LICI', 'BANDHANBNK', 'WIPRO', 'HDFCLIFE', 'KPITTECH', 'GRANULES', 'FEDERALBNK', 'SBICARD', 'BRITANNIA', 'HCLTECH', 'SYNGENE', '360ONE', 'JSWENERGY', 'KOTAKBANK', 'BANKINDIA', 'SJVN', 'HUDCO', 'HAVELLS', 'POONAWALLA', 'IDFCFIRSTB', 'TATAELXSI', 'AXISBANK', 'IRFC', 'OFSS', 'AUBANK', 'TORNTPOWER', 'SIEMENS', 'UPL', 'VEDL', 'PHOENIXLTD', 'ASTRAL', 'CAMS', 'CROMPTON', 'BPCL', 'HINDUNILVR', 'BANKBARODA', 'AMBUJACEM', 'MFSL', 'PEL', 'NATIONALUM', 'GODREJCP', 'PAYTM', 'APOLLOHOSP', 'TATAMOTORS', 'HINDCOPPER', 'CANBK', 'NMDC', 'MPHASIS', 'DRREDDY', 'TVSMOTOR', 'GRASIM', 'JSL', 'KFINTECH', 'COFORGE', 'BSOFT', 'OBEROIRLTY', 'CESC', 'NBCC', 'HINDPETRO', 'AUROPHARMA', 'MUTHOOTFIN', 'UNITDSPR', 'LT', 'ETERNAL', 'CDSL', 'SRF', 'NIFTYNXT50', 'TITAGARH', 'AARTIIND', 'MANAPPURAM', 'LODHA', 'NCC', 'PNBHOUSING', 'POWERGRID', 'SBIN', 'PATANJALI', 'HEROMOTOCO', 'MAXHEALTH', 'ADANIENSOL', 'ICICIGI', 'RELIANCE', 'ASIANPAINT', 'PETRONET', 'PRESTIGE', 'EICHERMOT', 'MIDCPNIFTY', 'ABB', 'INDHOTEL', 'PFC', 'NIFTY', 'APLAPOLLO', 'MGL', 'PIIND', 'TCS', 'MANKIND', 'JSWSTEEL', 'SAIL', 'VOLTAS', 'BOSCHLTD', 'ABFRL', 'DELHIVERY', 'ABCAPITAL', 'POLICYBZR', 'UNIONBANK', 'GLENMARK', 'GMRAIRPORT', 'DMART', 'MCX', 'INOXWIND', 'ALKEM', 'FINNIFTY', 'BEL', 'ITC', 'CONCOR', 'UNOMINDA', 'BALKRISIND', 'PAGEIND', 'CHOLAFIN', 'POLYCAB', 'ZYDUSLIFE', 'TITAN', 'TATACHEM', 'CIPLA', 'IOC', 'AMBER', 'MOTHERSON', 'TATATECH', 'TORNTPHARM', 'JIOFIN', 'MARUTI', 'ADANIGREEN', 'BHEL', 'DALBHARAT', 'DIVISLAB', 'FORTIS', 'PIDILITIND', 'PGEL', 'SHRIRAMFIN', 'PPLPHARMA', 'JUBLFOOD', 'INDUSTOWER', 'TRENT', 'SONACOMS', 'NTPC', 'GAIL', 'RBLBANK', 'ATGL', 'ANGELONE', 'HINDALCO', 'BHARTIARTL', 'JINDALSTEL', 'PERSISTENT', 'LUPIN', 'HINDZINC', 'CYIENT', 'COALINDIA', 'TATACONSUM', 'NYKAA', 'SBILIFE', 'MARICO', 'CHAMBLFERT', 'BANKNIFTY', 'DLF', 'ADANIPORTS', 'OIL', 'SHREECEM', 'HFCL', 'ASHOKLEY', 'RECLTD', 'IRB', 'TECHM', 'DABUR', 'KALYANKJIL', 'ACC', 'CGPOWER', 'COLPAL', 'BDL', 'KEI', 'LAURUSLABS', 'ONGC', 'LICHSGFIN', 'NESTLEIND', 'RVNL', 'CUMMINSIND', 'SUNPHARMA', 'YESBANK', 'HDFCBANK', 'M&MFIN', 'VBL', 'BAJAJFINSV', 'EXIDEIND', 'GODREJPROP', 'NAUKRI', 'SOLARINDS', 'LTIM', 'DIXON', 'IGL', 'MAZDOCK', 'KAYNES', 'INFY', 'BSE', 'TATAPOWER', 'TIINDIA', 'ICICIBANK', 'INDUSINDBK', 'M&M', 'BLUESTARCO']
symbol_parser - INFO - Saved 72382 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 72382 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 50 symbols
fyers_client - INFO - Fetching quotes for batch 73: 50 symbols
fyers_client - INFO - Fetching quotes for batch 74: 50 symbols
fyers_client - INFO - Fetching quotes for batch 75: 50 symbols
fyers_client - INFO - Fetching quotes for batch 76: 50 symbols
fyers_client - INFO - Fetching quotes for batch 77: 50 symbols
fyers_client - INFO - Fetching quotes for batch 78: 50 symbols
fyers_client - INFO - Fetching quotes for batch 79: 50 symbols
fyers_client - INFO - Fetching quotes for batch 80: 50 symbols
fyers_client - INFO - Fetching quotes for batch 81: 50 symbols
fyers_client - INFO - Fetching quotes for batch 82: 50 symbols
fyers_client - INFO - Fetching quotes for batch 83: 50 symbols
fyers_client - INFO - Fetching quotes for batch 84: 50 symbols
fyers_client - INFO - Fetching quotes for batch 85: 50 symbols
fyers_client - INFO - Fetching quotes for batch 86: 50 symbols
fyers_client - INFO - Fetching quotes for batch 87: 50 symbols
fyers_client - INFO - Fetching quotes for batch 88: 50 symbols
fyers_client - INFO - Fetching quotes for batch 89: 50 symbols
fyers_client - INFO - Fetching quotes for batch 90: 50 symbols
fyers_client - INFO - Fetching quotes for batch 91: 50 symbols
fyers_client - INFO - Fetching quotes for batch 92: 50 symbols
fyers_client - INFO - Fetching quotes for batch 93: 50 symbols
fyers_client - INFO - Fetching quotes for batch 94: 50 symbols
fyers_client - INFO - Fetching quotes for batch 95: 50 symbols
fyers_client - INFO - Fetching quotes for batch 96: 50 symbols
fyers_client - INFO - Fetching quotes for batch 97: 50 symbols
fyers_client - INFO - Fetching quotes for batch 98: 50 symbols
fyers_client - INFO - Fetching quotes for batch 99: 50 symbols
fyers_client - INFO - Fetching quotes for batch 100: 50 symbols
fyers_client - INFO - Fetching quotes for batch 101: 50 symbols
fyers_client - INFO - Fetching quotes for batch 102: 50 symbols
fyers_client - INFO - Fetching quotes for batch 103: 50 symbols
fyers_client - INFO - Fetching quotes for batch 104: 50 symbols
fyers_client - INFO - Fetching quotes for batch 105: 50 symbols
fyers_client - INFO - Fetching quotes for batch 106: 50 symbols
fyers_client - INFO - Fetching quotes for batch 107: 50 symbols
fyers_client - INFO - Fetching quotes for batch 108: 50 symbols
fyers_client - INFO - Fetching quotes for batch 109: 50 symbols
fyers_client - INFO - Fetching quotes for batch 110: 50 symbols
fyers_client - INFO - Fetching quotes for batch 111: 50 symbols
fyers_client - INFO - Fetching quotes for batch 112: 50 symbols
fyers_client - INFO - Fetching quotes for batch 113: 50 symbols
fyers_client - INFO - Fetching quotes for batch 114: 50 symbols
fyers_client - INFO - Fetching quotes for batch 115: 50 symbols
fyers_client - INFO - Fetching quotes for batch 116: 50 symbols
fyers_client - INFO - Fetching quotes for batch 117: 50 symbols
fyers_client - INFO - Fetching quotes for batch 118: 50 symbols
fyers_client - INFO - Fetching quotes for batch 119: 50 symbols
fyers_client - INFO - Fetching quotes for batch 120: 50 symbols
fyers_client - INFO - Fetching quotes for batch 121: 50 symbols
fyers_client - INFO - Fetching quotes for batch 122: 50 symbols
fyers_client - INFO - Fetching quotes for batch 123: 50 symbols
fyers_client - INFO - Fetching quotes for batch 124: 50 symbols
fyers_client - INFO - Fetching quotes for batch 125: 50 symbols
fyers_client - INFO - Fetching quotes for batch 126: 50 symbols
fyers_client - INFO - Fetching quotes for batch 127: 50 symbols
fyers_client - INFO - Fetching quotes for batch 128: 50 symbols
fyers_client - INFO - Fetching quotes for batch 129: 50 symbols
fyers_client - INFO - Fetching quotes for batch 130: 50 symbols
fyers_client - INFO - Fetching quotes for batch 131: 50 symbols
fyers_client - INFO - Fetching quotes for batch 132: 50 symbols
fyers_client - INFO - Fetching quotes for batch 133: 50 symbols
fyers_client - INFO - Fetching quotes for batch 134: 50 symbols
fyers_client - INFO - Fetching quotes for batch 135: 50 symbols
fyers_client - INFO - Fetching quotes for batch 136: 50 symbols
fyers_client - INFO - Fetching quotes for batch 137: 50 symbols
fyers_client - INFO - Fetching quotes for batch 138: 50 symbols
fyers_client - INFO - Fetching quotes for batch 139: 50 symbols
fyers_client - INFO - Fetching quotes for batch 140: 50 symbols
fyers_client - INFO - Fetching quotes for batch 141: 50 symbols
fyers_client - INFO - Fetching quotes for batch 142: 50 symbols
fyers_client - INFO - Fetching quotes for batch 143: 50 symbols
fyers_client - INFO - Fetching quotes for batch 144: 50 symbols
fyers_client - INFO - Fetching quotes for batch 145: 50 symbols
fyers_client - INFO - Fetching quotes for batch 146: 50 symbols
fyers_client - INFO - Fetching quotes for batch 147: 50 symbols
fyers_client - INFO - Fetching quotes for batch 148: 50 symbols
fyers_client - INFO - Fetching quotes for batch 149: 50 symbols
fyers_client - INFO - Fetching quotes for batch 150: 50 symbols
fyers_client - INFO - Fetching quotes for batch 151: 50 symbols
fyers_client - INFO - Fetching quotes for batch 152: 50 symbols
fyers_client - INFO - Fetching quotes for batch 153: 50 symbols
fyers_client - INFO - Fetching quotes for batch 154: 50 symbols
fyers_client - INFO - Fetching quotes for batch 155: 50 symbols
fyers_client - INFO - Fetching quotes for batch 156: 50 symbols
fyers_client - INFO - Fetching quotes for batch 157: 50 symbols
fyers_client - INFO - Fetching quotes for batch 158: 50 symbols
fyers_client - INFO - Fetching quotes for batch 159: 50 symbols
fyers_client - INFO - Fetching quotes for batch 160: 50 symbols
fyers_client - INFO - Fetching quotes for batch 161: 50 symbols
fyers_client - INFO - Fetching quotes for batch 162: 50 symbols
fyers_client - INFO - Fetching quotes for batch 163: 50 symbols
fyers_client - INFO - Fetching quotes for batch 164: 50 symbols
fyers_client - INFO - Fetching quotes for batch 165: 50 symbols
fyers_client - INFO - Fetching quotes for batch 166: 50 symbols
fyers_client - INFO - Fetching quotes for batch 167: 50 symbols
fyers_client - INFO - Fetching quotes for batch 168: 50 symbols
fyers_client - INFO - Fetching quotes for batch 169: 50 symbols
fyers_client - INFO - Fetching quotes for batch 170: 50 symbols
fyers_client - INFO - Fetching quotes for batch 171: 50 symbols
fyers_client - INFO - Fetching quotes for batch 172: 50 symbols
fyers_client - INFO - Fetching quotes for batch 173: 50 symbols
fyers_client - INFO - Fetching quotes for batch 174: 50 symbols
fyers_client - INFO - Fetching quotes for batch 175: 50 symbols
fyers_client - INFO - Fetching quotes for batch 176: 50 symbols
fyers_client - INFO - Fetching quotes for batch 177: 50 symbols
fyers_client - INFO - Fetching quotes for batch 178: 50 symbols
fyers_client - INFO - Fetching quotes for batch 179: 50 symbols
fyers_client - INFO - Fetching quotes for batch 180: 50 symbols
fyers_client - INFO - Fetching quotes for batch 181: 50 symbols
fyers_client - INFO - Fetching quotes for batch 182: 50 symbols
fyers_client - INFO - Fetching quotes for batch 183: 50 symbols
fyers_client - INFO - Fetching quotes for batch 184: 50 symbols
fyers_client - INFO - Fetching quotes for batch 185: 50 symbols
fyers_client - INFO - Fetching quotes for batch 186: 50 symbols
fyers_client - INFO - Fetching quotes for batch 187: 50 symbols
fyers_client - INFO - Fetching quotes for batch 188: 50 symbols
fyers_client - INFO - Fetching quotes for batch 189: 50 symbols
fyers_client - INFO - Fetching quotes for batch 190: 50 symbols
fyers_client - INFO - Fetching quotes for batch 191: 50 symbols
fyers_client - INFO - Fetching quotes for batch 192: 50 symbols
fyers_client - INFO - Fetching quotes for batch 193: 50 symbols
fyers_client - INFO - Fetching quotes for batch 194: 50 symbols
fyers_client - INFO - Fetching quotes for batch 195: 50 symbols
fyers_client - INFO - Fetching quotes for batch 196: 50 symbols
fyers_client - INFO - Fetching quotes for batch 197: 50 symbols
fyers_client - INFO - Fetching quotes for batch 198: 50 symbols
fyers_client - INFO - Fetching quotes for batch 199: 50 symbols
fyers_client - INFO - Fetching quotes for batch 200: 50 symbols
fyers_client - INFO - Fetching quotes for batch 201: 50 symbols
fyers_client - INFO - Fetching quotes for batch 202: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 203: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 204: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 205: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 206: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 207: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 208: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 209: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 210: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 211: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 212: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 213: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 214: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 215: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 216: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 217: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 218: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 219: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 220: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 221: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 222: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 223: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 224: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 225: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 226: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 227: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 228: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 229: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 230: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 231: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 232: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 233: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 234: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 235: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 236: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 237: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 238: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 239: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 240: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 241: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 242: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 243: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 244: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 245: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 246: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 247: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 248: 50 symbols
fyers_client - ERROR - Failed to get quotes for batch: {'message': 'request limit reached', 'code': 429, 's': 'error'}
fyers_client - INFO - Fetching quotes for batch 249: 50 symbols
fyers_config - INFO - Application interrupted by user
